# 相机模块配置
camera:
  module: "connect.camera.PercipioCamera"  # 相机模块类路径
  params:
    color_mode: 0
      # 0 -size[2560x1920]      -        desc:yuyv 2560x1920
      # 1 -size[1920x1440]      -        desc:yuyv 1920x1440
      # 2 -size[1280x960]       -        desc:yuyv 1280x960
      # 3 -size[640x480]        -        desc:yuyv 640x480
      # 4 -size[2560x1920]      -        desc:CSI BAYER12GR_2560x1920
    depth_mode: 1 
        # 0 -size[640x480]        -        desc:DEPTH16_640x480
        # 1 -size[1280x960]       -        desc:DEPTH16_1280x960
        # 2 -size[320x240]        -        desc:DEPTH16_320x240
        
    color_registration: False
    depth_registration: True

    visualization:
      enabled: True  # 是否启用可视化
      save_path: ./log/camera
      save_image: True
      save_depth: True
      save_pointcloud: True
      save_xyz: True
      save_metadata: True