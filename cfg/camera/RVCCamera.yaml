# 相机模块配置
camera:
  module: "connect.camera.RVCCamera"  # 相机模块类路径
  params:
    camera_type: "X2"  # 相机类型 (X1 或 X2)
    camera_id: "LEFT"  # 相机ID (LEFT 或 RIGHT)
    exposure_time: 100  # 曝光时间
    # device_index: null  # 相机序号
    # device_sn: null  # 相机序列号
    visualization:
      enabled: True  # 是否启用可视化
      save_path: ./log/camera
      save_image: True
      save_depth: True
      save_pointcloud: True
      save_metadata: True