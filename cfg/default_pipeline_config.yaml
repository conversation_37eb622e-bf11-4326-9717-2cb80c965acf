# pipeline_config.yaml
# 全局配置
global:
  debug: True  # 启用调试模式
  log_level: "DEBUG"  # 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  output_dir: "./log"  # 输出目录
  max_objects: 5  # 最大处理物体数量


# 日志配置
logging:
  file_path: "./logs/pipeline.log"  # 日志文件路径
  max_files: 7  # 保留的日志文件数量
  file_size: 10  # 单个日志文件最大大小 (MB)
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"  # 日志格式
  console_output: True  # 是否输出到控制台

# 机械臂
robot:
  __BASIC__: "./cfg/robot/AUBORobot1.yaml"
  # __BASIC__: "./cfg/robot/URRobot2.yaml"

# 相机模块配置
camera:
  __BASIC__: "./cfg/camera/PercipioCamera.yaml"
  # __BASIC__: "./cfg/camera/RVCCamera.yaml"


# 分割模块配置
segmentation:
  module: "module.segmentation.FastSam"  # 分割模块类路径
  params:
    model_path: "/home/<USER>/Documents/zhou.tianyi2/FastSAM/weights/FastSAM-x.pt"  # 分割模型路径
    device: "cuda"  # 计算设备 (cuda/cpu)
    confidence_threshold: 0.5  # 置信度阈值
    iou: 0.9
    # classes: [0, 1, 2]  # 关注的类别ID
    prompt:
      text_prompt: null  # 文本提示
      # point_prompt: [[0, 0]]  # 点提示
      # point_label: [0]  # 点标签
      # box_prompt: [[0,0,0,0]]  # 框提示

    visualization:
      enabled: True  # 是否启用可视化
      save_path: ./log/segmentation
      save_images: True  # 是否保存可视化图像
      save_pointclouds: False  # 是否保存点云数据
      image_format: "jpg"  # 图像保存格式
      pointcloud_format: "ply"  # 点云保存格式
      show_live: False  # 是否实时显示可视化结果

# # 姿态估计模块配置
# pose_estimation:
#   module: "module.pose_estimation.GraspNet"  # 姿态估计模块类路径
#   params:
#     model_path: "models/pose/graspnet.pth"  # 姿态估计模型路径
#     device: "cuda"  # 计算设备
#     n_grasps_per_instance: 10  # 每个实例生成的抓取姿态数量
#     min_score: 0.4  # 最低姿态评分阈值
#     batch_size: 4  # 推理批大小
#     visualization:
#       enabled: True  # 是否启用可视化
#       save_images: True  # 是否保存可视化图像
#       save_pointclouds: False  # 是否保存点云数据
#       image_format: "jpg"  # 图像保存格式
#       pointcloud_format: "ply"  # 点云保存格式
#       show_live: False  # 是否实时显示可视化结果

pick_estimation:
  module: "module.pick_estimation.MorphologyPick"  # 姿态估计模块类路径
  params:
    # 抓取点评分权重(质心评分+法向量稳定性评分+远离边缘评分)
    distance_to_centroid_weight: 0.2
    normal_stability_weight: 0.5
    distance_to_edge_weight: 0.3
    # 生成抓取点属性
    generate_pose_type: 
      # name: SuckPose
      name: GraspPose
      gripper_depth: 0.01
      gripper_width: 0.04
    visualization:
      enabled: True  # 是否启用可视化
      save_path: ./log/pick_estimation

# 碰撞检测模块配置
collision_estimation:
  module: "module.collision_estimation.GraspMeshCollision"
  params:
    finger_width: 0.01
    finger_length: 0.06
    voxel_size: 0.005
    approach_dist: 0.03
    collision_thresh: 0.05
    empty_thresh: 0.01
    visualization:
      enabled: true
      save_path: ./log/collision_estimation

# 处理节点配置
processing:
  - function: "processor.scene_processor.register_extrinsics"
    stage: "after_capture"
    params:
      name: AUBORobot1_to_PercipioCamera
      file_path: "./cfg/calib/calib_result_1.json"
      type: json
      keyword: T

  - function: "processor.d2_processor.filter_2d"
    stage: "after_capture"
    params:
      2d_bbox_xyxy: [1080,600, 1860,1360]  # resolution [2560x1920]
      apply_image: True
      apply_depth: True
      apply_pointcloud: True
      apply_xyz: True
      apply_intrinsics: True

  # - function: "processor.d3_processor.filter_3d"
  #   stage: "after_capture"
  #   params:
  #     3d_bbox_xyzdxdydzrxryrz: [0,0,1, 1,1,1, 1.07,1.07,1.07]  # x, y, z, dx, dy, dz, rx, ry, rz
  #     apply_image: False
  #     apply_depth: False
  #     apply_pointcloud: True
  #     apply_xyz: False

  - function: "processor.instance_processor.filter_instances_by_mask_area"
    stage: "after_segmentation"
    params:
      min_mask_size: 100  # 最小掩码面积
      max_mask_size: 20000  # 最大掩码面积

  - function: "processor.instance_processor.set_instance_pointcloud_by_mask"
    stage: "after_segmentation"

  - function: "processor.instance_processor.set_instance_xyz_by_mask"
    stage: "after_segmentation"

  - function: "processor.instance_processor.filter_instances_by_pointcloud"
    stage: "after_segmentation"
    params:
      point_number: 50

  - function: "processor.instance_processor.sort_instances_by_mask_area"
    stage: "after_segmentation"
    params:
      descending: true  # 降序排序

  - function: "processor.instance_processor.filter_instance_by_order"
    stage: "after_segmentation"
    params:
      top_instances: 7
  - function: "processor.instance_processor.sort_instances_by_distance_image_center"
    stage: "after_segmentation"
    params:
      descending: true  # 降序排序

  - function: "processor.instance_processor.filter_instance_by_order"
    stage: "after_segmentation"
    params:
      top_instances: 7

  - function: "processor.scene_processor.extrinsics_transform"
    stage: "after_pick_estimation"
    # stage: "after_segmentation"
    params:
      reverse: False
      extrinsics: "extrinsics"
      apply_pose: True
      apply_pickposes: True
      apply_xyz: True
      apply_pointcloud: True

#   - function: "module.processor.pose_processor.limit_grasps_per_instance"
#     stage: "after_pose_estimation"
#     params:
#       max_grasps: 5  # 每个实例最大抓取姿态数

  - function: "processor.pose_processor.align_rotate"
    stage: "after_pick_estimation"
    params:
      gripper_align_axis: 2  # z
      world_target_axis: 2   # z
      gripper_rotate_axis: 1 # y
      invers: True

  # - function: "processor.pose_processor.filter_pickpose_by_collitsion_score"
  #   stage: "after_collision_estimation"
  #   params:
  #     score_threshold: 0.001
  #     # top: 1

  - function: "processor.instance_processor.sort_instance_by_collision_score"
    stage: "after_collision_estimation"