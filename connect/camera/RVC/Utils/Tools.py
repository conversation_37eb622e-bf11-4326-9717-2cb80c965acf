"""
Utility functions for RVC camera operations
"""

import datetime
import os
from pathlib import Path


def TryCreateDir(directory_path):
    """
    Create directory if it doesn't exist

    Args:
        directory_path (str): Path to the directory to create

    Returns:
        bool: True if directory exists or was created successfully, False otherwise
    """
    try:
        if not os.path.exists(directory_path):
            os.makedirs(directory_path, exist_ok=True)
            print(f"Created directory: {directory_path}")
        return True
    except Exception as e:
        print(f"Failed to create directory {directory_path}: {e}")
        return False


def generate_timestamp_filename(prefix="", suffix="", extension="png"):
    """
    Generate a filename with timestamp

    Args:
        prefix (str): Prefix for the filename
        suffix (str): Suffix for the filename
        extension (str): File extension

    Returns:
        str: Filename with timestamp
    """
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    if prefix and suffix:
        return f"{prefix}_{timestamp}_{suffix}.{extension}"
    elif prefix:
        return f"{prefix}_{timestamp}.{extension}"
    elif suffix:
        return f"{timestamp}_{suffix}.{extension}"
    else:
        return f"{timestamp}.{extension}"


def create_save_directory_structure(base_dir, device_sn, sub_dirs=None):
    """
    Create a standardized directory structure for saving RVC camera data

    Args:
        base_dir (str): Base directory path
        device_sn (str): Device serial number
        sub_dirs (list): List of subdirectories to create

    Returns:
        str: Final directory path
    """
    if sub_dirs is None:
        sub_dirs = []

    # Create base directory
    TryCreateDir(base_dir)

    # Create device-specific directory
    device_dir = os.path.join(base_dir, device_sn)
    TryCreateDir(device_dir)

    # Create subdirectories
    final_dir = device_dir
    for sub_dir in sub_dirs:
        final_dir = os.path.join(final_dir, sub_dir)
        TryCreateDir(final_dir)

    return final_dir
