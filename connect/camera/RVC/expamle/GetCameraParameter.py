# Copyright (c) RVBUST, Inc - All rights reserved.
import os

import cv2
import numpy as np
import PyRVC as RVC
from Utils.Tools import *


def App():

    # Initialize RVC X system.
    RVC.SystemInit()

    # Choose RVC X Camera type (USB, GigE or All)
    opt = RVC.SystemListDeviceTypeEnum.All

    # Scan all RVC X Camera devices.
    ret, devices = RVC.SystemListDevices(opt)
    print("RVC X Camera devices number:%d" % len(devices))

    #  Find whether any RVC X Camera is connected or not.
    if len(devices) == 0:
        print("Can not find any RVC X Camera!")
        RVC.SystemShutdown()
        return 1
    print("devices size = %d" % len(devices))

    # Create a RVC X Camera and choose use left side camera.
    x = RVC.X2.Create(devices[0], RVC.CameraID_Left)
    # x = RVC.X2.Create(devices[0])

    # Test RVC X Camera is valid or not.
    if x.IsValid() == True:
        print("RVC X Camera is valid!")
    else:
        print("RVC X Camera is not valid!")
        RVC.X2.Destroy(x)
        RVC.SystemShutdown()
        return 1
    # PrintCaptureMode(devices[0])

    x.Open()

    if x.<PERSON>() == False:
        print("RVC X Camera is not opened!")
        RVC.X2.Destroy(x)
        RVC.SystemShutdown()
        exit(1)

    # ret, T_extrinsic = x.GetExtrinsicMatrix()
    # if ret == True:
    #     print("\nExtrinsicMatrix:")
    #     print(T_extrinsic)

    ret, T_intrinsic_matrix, T_distortion = x.GetIntrinsicParameters()
    if ret == True:
        print("\nIntrinsicParameters:")
        print(T_intrinsic_matrix)
        print("\ndistortion:")
        print(T_distortion)

    # Close RVC X Camera.
    x.Close()

    # Destroy RVC X Camera.
    RVC.X2.Destroy(x)

    # Shutdown RVC X System.
    RVC.SystemShutdown()

    return 0


if __name__ == "__main__":
    App()
