# RVC Camera Wrapper

一个用于RVC X1/X2相机的Python封装类，简化了相机初始化、配置和数据捕获操作。

## 特性

- **统一接口**: 支持X1和X2相机的统一API
- **自动资源管理**: 使用上下文管理器自动处理初始化和清理
- **错误处理**: 完善的异常处理和错误信息
- **灵活配置**: 支持多种配置选项和参数设置
- **数据保存**: 自动化的数据保存和目录管理
- **连续捕获**: 支持实时显示和自动保存的连续捕获
- **便捷函数**: 提供快速使用的便捷函数

## 安装要求

- Python 3.6+
- PyRVC库
- OpenCV (cv2)
- NumPy

## 快速开始

### 基本使用

```python
from rvc_camera_wrapper import RVCCameraWrapper, CameraType

# 使用上下文管理器（推荐）
with RVCCameraWrapper(CameraType.X2) as camera:
    # 捕获单帧
    if camera.capture_single():
        # 获取图像数据
        img = camera.get_image()
        
        # 获取点云数据
        pointmap = camera.get_point_map()
        
        # 保存所有数据
        save_dir = camera.save_data("Data/my_capture")
        print(f"数据已保存到: {save_dir}")
```

### 便捷函数

```python
from rvc_camera_wrapper import quick_capture_x2, quick_capture_x1

# 快速X2捕获
save_dir = quick_capture_x2(save_dir="Data/quick_test")

# 快速X1捕获
save_dir = quick_capture_x1(save_dir="Data/quick_test")
```

## 详细使用说明

### 1. 相机初始化

#### 使用设备索引
```python
camera = RVCCameraWrapper(CameraType.X2, device_index=0)
```

#### 使用设备序列号（推荐用于多相机）
```python
camera = RVCCameraWrapper(CameraType.X2, device_sn="M2GM012W019")
```

#### 指定相机ID
```python
from rvc_camera_wrapper import CameraID
camera = RVCCameraWrapper(CameraType.X2, camera_id=CameraID.LEFT)
```

### 2. 相机配置

#### 设置曝光时间
```python
camera.set_exposure_time(100)  # 设置曝光时间为100
```

#### 获取相机参数
```python
intrinsic, distortion = camera.get_camera_parameters()
print("内参矩阵:", intrinsic)
print("畸变系数:", distortion)
```

### 3. 数据捕获

#### 单次捕获
```python
if camera.capture_single():
    # 获取图像
    img = camera.get_image()
    
    # 获取点云
    pointmap = camera.get_point_map()
    
    # 获取深度图
    depth_map = camera.get_depth_map()
```

#### 连续捕获
```python
# 每10帧保存一次，最多捕获100帧，显示实时画面
frames_captured = camera.continuous_capture(
    save_interval=10,
    max_frames=100,
    display=True,
    save_dir="Data/continuous"
)
```

### 4. 数据保存

#### 保存所有数据
```python
save_dir = camera.save_data("Data/my_data")
```

#### 自定义保存选项
```python
save_dir = camera.save_data(
    save_dir="Data/custom",
    prefix="test_",
    save_image=True,
    save_pointmap=True,
    save_depthmap=False,
    save_colored_pointmap=True
)
```

## API参考

### RVCCameraWrapper类

#### 构造函数
```python
RVCCameraWrapper(
    camera_type: CameraType = CameraType.X2,
    device_index: int = 0,
    device_sn: Optional[str] = None,
    camera_id: CameraID = CameraID.LEFT
)
```

#### 主要方法

- `initialize() -> bool`: 初始化相机
- `cleanup()`: 清理资源
- `is_ready() -> bool`: 检查相机是否就绪
- `set_exposure_time(exposure_time: int) -> bool`: 设置曝光时间
- `capture_single() -> bool`: 单次捕获
- `get_image() -> Optional[np.ndarray]`: 获取图像
- `get_point_map() -> Optional[np.ndarray]`: 获取点云
- `get_depth_map()`: 获取深度图
- `get_camera_parameters() -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]`: 获取相机参数
- `save_data() -> str`: 保存数据
- `continuous_capture() -> int`: 连续捕获
- `get_device_info() -> dict`: 获取设备信息

### 枚举类型

#### CameraType
- `CameraType.X1`: X1相机
- `CameraType.X2`: X2相机

#### CameraID
- `CameraID.LEFT`: 左相机
- `CameraID.RIGHT`: 右相机

## 示例代码

查看 `example_usage.py` 文件获取完整的使用示例，包括：

- 基本使用方法
- 手动初始化和清理
- 指定设备序列号
- 连续捕获
- X1相机使用
- 便捷函数使用
- 错误处理
- 自定义保存选项

## 目录结构

保存的数据将按以下结构组织：

```
Data/
├── {device_sn}/
│   ├── x1/ 或 x2/
│   │   ├── {timestamp}/
│   │   │   ├── image.png
│   │   │   ├── pointmap.ply
│   │   │   ├── pointmap_color.ply
│   │   │   └── depthmap.tiff
│   │   └── continuous/
│   │       └── {timestamp}/
│   │           ├── frame_0001_*.png
│   │           └── ...
```

## 错误处理

封装类提供了完善的错误处理：

- 自动检查相机连接状态
- 详细的错误信息输出
- 安全的资源清理
- 异常情况下的优雅降级

## 注意事项

1. **设备权限**: 确保有访问相机设备的权限
2. **资源管理**: 推荐使用上下文管理器自动管理资源
3. **多相机**: 使用设备序列号区分多个相机
4. **性能**: 连续捕获时注意存储空间和性能

## 故障排除

### 常见问题

1. **找不到设备**
   - 检查相机连接
   - 确认设备序列号正确
   - 检查设备权限

2. **初始化失败**
   - 确认PyRVC库正确安装
   - 检查相机驱动
   - 确认设备支持所选相机类型

3. **捕获失败**
   - 检查相机是否正确初始化
   - 确认曝光时间设置合理
   - 检查设备连接稳定性

## 许可证

基于原始RVC示例代码开发，遵循相同的许可证条款。
