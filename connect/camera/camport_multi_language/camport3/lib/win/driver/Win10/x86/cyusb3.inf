; Installation INF for the Cypress Generic USB Driver for %OPERATING_SYSTEM%
; Processor support for %PLATFORM% platforms.
;
; (c) Copyright 2017 Cypress Semiconductor Corporation
;

[Version]
Signature="$WINDOWS NT$"
Class=USB
ClassGUID={36FC9E60-C465-11CF-8056-************}
provider=%CYUSB3_Provider%
CatalogFile=percipioxyz.cat
DriverVer=11/08/2017,********

[SourceDisksNames]
1=%CYUSB3_Install%,,,

[SourceDisksFiles]
CYUSB3.sys = 1
;


[DestinationDirs]
CYUSB3.Files.Ext = 10,System32\Drivers
CoInstaller_CopyFiles = 11

[ControlFlags]
ExcludeFromSelect = *

[Manufacturer]
%CYUSB3_Provider%=Device,NTx86,NTamd64

;for x86 platforms
[Device.NTx86]
;%VID_XXXX&PID_XXXX.DeviceDesc%=CyUsb3, USB\VID_XXXX&PID_XXXX
%VID_04B4&PID_1003.DeviceDesc%=CyUsb3, USB\VID_04B4&PID_1003

;for x64 platforms
[Device.NTamd64]
;%VID_XXXX&PID_XXXX.DeviceDesc%=CyUsb3, USB\VID_XXXX&PID_XXXX
%VID_04B4&PID_1003.DeviceDesc%=CyUsb3, USB\VID_04B4&PID_1003

[CYUSB3.NTx86]
CopyFiles=CYUSB3.Files.Ext
AddReg=CyUsb3.AddReg

[CYUSB3.NTx86.HW]
AddReg=CYUSB3.AddReg.Guid

[CYUSB3.NTx86.Services]
Addservice = CYUSB3,2,CYUSB3.AddService

[CYUSB3.NTamd64]
CopyFiles=CYUSB3.Files.Ext
AddReg=CyUsb3.AddReg

[CYUSB3.NTamd64.HW]
AddReg=CYUSB3.AddReg.Guid

[CYUSB3.NTamd64.Services]
Addservice = CYUSB3,2,CYUSB3.AddService


[CYUSB3.AddReg]
; Deprecating - do not use in new apps to identify a CYUSB3 driver
HKR,,DevLoader,,*ntkern
HKR,,NTMPDriver,,CYUSB3.sys
; You may optionally include a check for DriverBase in your application to check for a CYUSB3 driver
HKR,,DriverBase,,CYUSB3.sys
HKR,"Parameters","MaximumTransferSize",0x10001,4096
HKR,"Parameters","DebugLevel",0x10001,2
HKR,,FriendlyName,,%CYUSB3_Description%

[CYUSB3.AddService]
DisplayName    = %CYUSB3_Description%
ServiceType    = 1                  ; SERVICE_KERNEL_DRIVER
StartType      = 3                  ; SERVICE_DEMAND_START
ErrorControl   = 1                  ; SERVICE_ERROR_NORMAL
ServiceBinary  = %10%\System32\Drivers\CYUSB3.sys
AddReg         = CYUSB3.AddReg
LoadOrderGroup = Base

[CYUSB3.Files.Ext]
CYUSB3.sys

[CYUSB3.AddReg.Guid]
HKR,,DriverGUID,,%CYUSB3.GUID%
;HKR,,DriverPowerPolicySetup,,%ENABLE_DRIVER_POWER_POLICY%

;-------------- WDF Coinstaller installation
[CYUSB3.NTamd64.CoInstallers]
AddReg=CoInstaller_AddReg
CopyFiles=CoInstaller_CopyFiles

[CYUSB3.NTx86.CoInstallers]
AddReg=CoInstaller_AddReg
CopyFiles=CoInstaller_CopyFiles

[CoInstaller_CopyFiles]
;


[CoInstaller_AddReg]
;


[CYUSB3.NTamd64.Wdf]
KmdfService = CYUSB3, CYUSB3_wdfsect

[CYUSB3.NTx86.Wdf]
KmdfService = CYUSB3, CYUSB3_wdfsect

[CYUSB3_wdfsect]
KmdfLibraryVersion = 1.15


[Strings]
CYUSB3_Provider    = "Percipio"
CYUSB3_Company     = "Percipio Semiconductor Corporation"
CYUSB3_Description = "Percipio Generic USB3.0 Driver"
CYUSB3_DisplayName = "Percipio USB3.0 Generic"
CYUSB3_Install     = "Percipio CYUSB3.0 Driver Installation Disk"
VID_XXXX&PID_XXXX.DeviceDesc="Percipio USB3.0 Generic Driver"
VID_04B4&PID_1003.DeviceDesc="Percipio Device"

CYUSB3.GUID="{AE18AA60-7F6A-11d4-97DD-00010229B959}"
;ENABLE_DRIVER_POWER_POLICY="1"
CYUSB3_Unused      = "."
