"""
Description:
Author: zxy
Date: 2023-07-13 15:38:51
LastEditors: zxy
LastEditTime: 2023-07-18 11:57:37
"""

import os
import sys

import cv2
import numpy
import pcammls
from pcammls import *


class PythonPercipioDeviceEvent(pcammls.DeviceEvent):
    Offline = False

    def __init__(self):
        pcammls.DeviceEvent.__init__(self)

    def run(self, handle, eventID):
        if eventID == TY_EVENT_DEVICE_OFFLINE:
            print("=== Event Callback: Device Offline!")
            self.Offline = True
        return 0

    def IsOffline(self):
        return self.Offline


def main():
    cl = PercipioSDK()

    dev_list = cl.ListDevice()
    for idx in range(len(dev_list)):
        dev = dev_list[idx]
        print("{} -- {} \t {}".format(idx, dev.id, dev.iface.id))
    if len(dev_list) == 0:
        print("no device")
        return
    if len(dev_list) == 1:
        selected_idx = 0
    else:
        selected_idx = int(input("select a device:"))
    if selected_idx < 0 or selected_idx >= len(dev_list):
        return

    sn = dev_list[selected_idx].id

    handle = cl.Open(sn)
    if not cl.isValidHandle(handle):
        err = cl.TYGetLastErrorCodedescription()
        print("no device found : ", end="")
        print(err)
        return

    event = PythonPercipioDeviceEvent()
    cl.DeviceRegiststerCallBackEvent(event)

    depth_fmt_list = cl.DeviceStreamFormatDump(handle, PERCIPIO_STREAM_DEPTH)
    if len(depth_fmt_list) == 0:
        print("device has no depth stream.")
        return

    print("depth image format list:")
    for idx in range(len(depth_fmt_list)):
        fmt = depth_fmt_list[idx]
        print(
            "\t{} -size[{}x{}]\t-\t desc:{}".format(
                idx, cl.Width(fmt), cl.Height(fmt), fmt.getDesc()
            )
        )
    cl.DeviceStreamFormatConfig(handle, PERCIPIO_STREAM_DEPTH, depth_fmt_list[0])

    cl.DeviceControlTriggerModeEnable(handle, 1)

    err = cl.DeviceLoadDefaultParameters(handle)
    if err:
        print("Load default parameters fail: ", end="")
        print(cl.TYGetLastErrorCodedescription())
    else:
        print("Load default parameters successful")

    err = cl.DeviceStreamEnable(handle, PERCIPIO_STREAM_DEPTH)
    if err:
        print("device stream enable err:{}".format(err))
        return

    depth_render = image_data()
    cl.DeviceStreamOn(handle)

    while True:
        if event.IsOffline():
            break

        cl.DeviceControlTriggerModeSendTriggerSignal(handle)
        image_list = cl.DeviceStreamRead(handle, 20000)
        for i in range(len(image_list)):
            frame = image_list[i]
            arr = frame.as_nparray()
            if frame.streamID == PERCIPIO_STREAM_DEPTH:
                cl.DeviceStreamDepthRender(frame, depth_render)
                mat_depth_render = depth_render.as_nparray()
                cv2.imshow("depth", mat_depth_render)
        k = cv2.waitKey(10)
        if k == ord("q"):
            break

    cl.DeviceStreamOff(handle)
    cl.Close(handle)
    pass


if __name__ == "__main__":
    main()
