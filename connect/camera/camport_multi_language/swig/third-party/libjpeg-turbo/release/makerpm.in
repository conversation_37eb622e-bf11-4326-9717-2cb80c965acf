#!/bin/sh

set -u
set -e
trap onexit INT
trap onexit TERM
trap onexit EXIT

TMPDIR=

onexit()
{
	if [ ! "$TMPDIR" = "" ]; then
		rm -rf $TMPDIR
	fi
}

if [ -f @PKGNAME@-@VERSION@.@R<PERSON><PERSON>CH@.rpm ]; then
	rm -f @PKGNAME@-@VERSION@.@RPMARCH@.rpm
fi

umask 022
TMPDIR=`mktemp -d /tmp/@<EMAIL>`

mkdir -p $TMPDIR/RPMS
ln -fs `pwd` $TMPDIR/BUILD
rpmbuild -bb --define "_blddir $TMPDIR/buildroot" --define "_topdir $TMPDIR" \
	--target @RPMARCH@ pkgscripts/rpm.spec; \
cp $TMPDIR/RPMS/@RPMARCH@/@PKGNAME@-@VERSION@-@BUILD@.@R<PERSON>ARCH@.rpm \
	@PKGNAME@-@VERSION@.@<PERSON><PERSON><PERSON><PERSON>@.rpm
