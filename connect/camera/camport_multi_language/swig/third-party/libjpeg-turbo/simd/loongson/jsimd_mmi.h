/*
 * Loongson MMI optimizations for libjpeg-turbo
 *
 * Copyright (C) 2016-2017, Loongson Technology Corporation Limited, BeiJing.
 *                          All Rights Reserved.
 * Authors: <AUTHORS>
 *           <PERSON>aiWanwei   <<EMAIL>>
 *           SunZhangzhi <<EMAIL>>
 *
 * This software is provided 'as-is', without any express or implied
 * warranty.  In no event will the authors be held liable for any damages
 * arising from the use of this software.
 *
 * Permission is granted to anyone to use this software for any purpose,
 * including commercial applications, and to alter it and redistribute it
 * freely, subject to the following restrictions:
 *
 * 1. The origin of this software must not be misrepresented; you must not
 *    claim that you wrote the original software. If you use this software
 *    in a product, an acknowledgment in the product documentation would be
 *    appreciated but is not required.
 * 2. Altered source versions must be plainly marked as such, and must not be
 *    misrepresented as being the original software.
 * 3. This notice may not be removed or altered from any source distribution.
 */

#define JPEG_INTERNALS
#include "../../jinclude.h"
#include "../../jpeglib.h"
#include "../../jdct.h"
#include "loongson-mmintrin.h"


/* Common code */

#define SIZEOF_MMWORD  8
#define BYTE_BIT  8
#define WORD_BIT  16
#define SCALEBITS  16

#define _uint64_set_pi8(a, b, c, d, e, f, g, h) \
  (((uint64_t)(uint8_t)a << 56) | \
   ((uint64_t)(uint8_t)b << 48) | \
   ((uint64_t)(uint8_t)c << 40) | \
   ((uint64_t)(uint8_t)d << 32) | \
   ((uint64_t)(uint8_t)e << 24) | \
   ((uint64_t)(uint8_t)f << 16) | \
   ((uint64_t)(uint8_t)g << 8)  | \
   ((uint64_t)(uint8_t)h))
#define _uint64_set_pi16(a, b, c, d)  (((uint64_t)(uint16_t)a << 48) | \
                                       ((uint64_t)(uint16_t)b << 32) | \
                                       ((uint64_t)(uint16_t)c << 16) | \
                                       ((uint64_t)(uint16_t)d))
#define _uint64_set_pi32(a, b)  (((uint64_t)(uint32_t)a << 32) | \
                                 ((uint64_t)(uint32_t)b))

#define get_const_value(index)  (*(__m64 *)&const_value[index])
